import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np

class PPOTrainer:
    def __init__(self, model, lr=3e-4, gamma=0.99, eps_clip=0.2, k_epochs=4, 
                 value_loss_coef=0.5, entropy_coef=0.01):
        self.model = model
        self.optimizer = optim.Adam(model.parameters(), lr=lr)
        
        # PPO hyperparameters
        self.gamma = gamma
        self.eps_clip = eps_clip
        self.k_epochs = k_epochs
        self.value_loss_coef = value_loss_coef
        self.entropy_coef = entropy_coef
        
        self.mse_loss = nn.MSELoss()
    
    def compute_gae(self, rewards, values, dones, next_value, gae_lambda=0.95):
        """Compute Generalized Advantage Estimation"""
        advantages = []
        gae = 0
        
        # Add next_value to values for easier computation
        values = values + [next_value]
        
        for step in reversed(range(len(rewards))):
            if step == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[step]
                next_value = values[step + 1]
            else:
                next_non_terminal = 1.0 - dones[step]
                next_value = values[step + 1]
            
            delta = rewards[step] + self.gamma * next_value * next_non_terminal - values[step]
            gae = delta + self.gamma * gae_lambda * next_non_terminal * gae
            advantages.insert(0, gae)
        
        return advantages
    
    def compute_returns(self, rewards, values, dones, next_value):
        """Compute discounted returns"""
        returns = []
        discounted_return = next_value
        
        for step in reversed(range(len(rewards))):
            if dones[step]:
                discounted_return = 0
            discounted_return = rewards[step] + self.gamma * discounted_return
            returns.insert(0, discounted_return)
        
        return returns
    
    def train_step(self, states, actions, old_log_probs, rewards, dones, values):
        """PPO training step"""
        # Convert to tensors
        states = torch.FloatTensor(states)
        actions = torch.LongTensor(actions)
        old_log_probs = torch.FloatTensor(old_log_probs)
        rewards = torch.FloatTensor(rewards)
        dones = torch.BoolTensor(dones)
        old_values = torch.FloatTensor(values)
        
        # Compute next value (0 for terminal states)
        with torch.no_grad():
            _, next_value = self.model(states[-1:])
            next_value = next_value.item() if not dones[-1] else 0
        
        # Compute advantages and returns
        advantages = self.compute_gae(rewards.tolist(), old_values.tolist(), 
                                    dones.tolist(), next_value)
        advantages = torch.FloatTensor(advantages)
        
        returns = self.compute_returns(rewards.tolist(), old_values.tolist(), 
                                     dones.tolist(), next_value)
        returns = torch.FloatTensor(returns)
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # PPO training loop
        for _ in range(self.k_epochs):
            # Evaluate current policy
            log_probs, state_values, entropy = self.model.evaluate_actions(states, actions)
            
            # Compute ratio (pi_theta / pi_theta_old)
            ratio = torch.exp(log_probs - old_log_probs)
            
            # Compute surrogate losses
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 1 - self.eps_clip, 1 + self.eps_clip) * advantages
            
            # PPO loss
            actor_loss = -torch.min(surr1, surr2).mean()
            critic_loss = self.mse_loss(state_values, returns)
            entropy_loss = -entropy.mean()
            
            total_loss = (actor_loss + 
                         self.value_loss_coef * critic_loss + 
                         self.entropy_coef * entropy_loss)
            
            # Update network
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 0.5)
            self.optimizer.step()
        
        return {
            'actor_loss': actor_loss.item(),
            'critic_loss': critic_loss.item(),
            'entropy_loss': entropy_loss.item(),
            'total_loss': total_loss.item()
        }
