import matplotlib.pyplot as plt
import numpy as np
from agent import Agent, train as train_dqn
from ppo_agent import PPOAgent, train_ppo
import torch
import time

def run_comparison():
    """Compare DQN vs PPO performance"""
    print("=== Comparison of DQN vs PPO ===")
    print("1. Train DQN")
    print("2. Train PPO") 
    print("3. Load and compare saved models")
    
    choice = input("Choose option (1/2/3): ")
    
    if choice == "1":
        print("\n--- Training DQN ---")
        start_time = time.time()
        train_dqn()
        dqn_time = time.time() - start_time
        print(f"DQN training completed in {dqn_time:.2f} seconds")
        
    elif choice == "2":
        print("\n--- Training PPO ---")
        start_time = time.time()
        train_ppo()
        ppo_time = time.time() - start_time
        print(f"PPO training completed in {ppo_time:.2f} seconds")
        
    elif choice == "3":
        print("\n--- Comparing saved models ---")
        compare_saved_models()
    
    else:
        print("Invalid choice!")

def compare_saved_models():
    """Load and test both saved models"""
    from game import SnakeGameAI
    
    # Test DQN
    print("Testing DQN model...")
    dqn_agent = Agent()
    try:
        dqn_agent.model.load_state_dict(torch.load('./model/model.pth'))
        dqn_scores = test_agent(dqn_agent, "DQN", num_games=10)
    except:
        print("DQN model not found!")
        dqn_scores = []
    
    # Test PPO
    print("Testing PPO model...")
    ppo_agent = PPOAgent()
    try:
        ppo_agent.model.load('./model/ppo_model.pth')
        ppo_scores = test_agent_ppo(ppo_agent, "PPO", num_games=10)
    except:
        print("PPO model not found!")
        ppo_scores = []
    
    # Compare results
    if dqn_scores and ppo_scores:
        print(f"\n=== Results ===")
        print(f"DQN - Mean: {np.mean(dqn_scores):.2f}, Std: {np.std(dqn_scores):.2f}, Max: {max(dqn_scores)}")
        print(f"PPO - Mean: {np.mean(ppo_scores):.2f}, Std: {np.std(ppo_scores):.2f}, Max: {max(ppo_scores)}")
        
        # Plot comparison
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.bar(['DQN', 'PPO'], [np.mean(dqn_scores), np.mean(ppo_scores)])
        plt.title('Average Score Comparison')
        plt.ylabel('Average Score')
        
        plt.subplot(1, 2, 2)
        plt.boxplot([dqn_scores, ppo_scores], labels=['DQN', 'PPO'])
        plt.title('Score Distribution')
        plt.ylabel('Score')
        
        plt.tight_layout()
        plt.show()

def test_agent(agent, name, num_games=10):
    """Test DQN agent"""
    from game import SnakeGameAI
    
    scores = []
    game = SnakeGameAI()
    
    for i in range(num_games):
        game.reset()
        score = 0
        
        while True:
            state = agent.get_state(game)
            final_move = agent.get_action(state)
            reward, done, score = game.play_step(final_move)
            
            if done:
                scores.append(score)
                print(f"{name} Game {i+1}: Score = {score}")
                break
    
    return scores

def test_agent_ppo(agent, name, num_games=10):
    """Test PPO agent"""
    from game import SnakeGameAI
    
    scores = []
    game = SnakeGameAI()
    
    for i in range(num_games):
        game.reset()
        score = 0
        
        while True:
            state = agent.get_state(game)
            action, _, _ = agent.get_action(state)
            final_move = agent.action_to_move(action)
            reward, done, score = game.play_step(final_move)
            
            if done:
                scores.append(score)
                print(f"{name} Game {i+1}: Score = {score}")
                break
    
    return scores

def analyze_training_curves():
    """Analyze and plot training curves if available"""
    # This would require saving training data during training
    # For now, just a placeholder
    print("Training curve analysis would require saving training data...")
    print("Consider modifying the training functions to save scores to files.")

if __name__ == '__main__':
    run_comparison()
