import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import os

class PPOActorCritic(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super(PPOActorCritic, self).__init__()
        
        # Shared layers
        self.shared_layer = nn.Linear(input_size, hidden_size)
        
        # Actor network (policy)
        self.actor = nn.Linear(hidden_size, output_size)
        
        # Critic network (value function)
        self.critic = nn.Linear(hidden_size, 1)
        
    def forward(self, x):
        # Shared feature extraction
        shared_features = F.relu(self.shared_layer(x))
        
        # Actor output (action probabilities)
        action_logits = self.actor(shared_features)
        action_probs = F.softmax(action_logits, dim=-1)
        
        # Critic output (state value)
        state_value = self.critic(shared_features)
        
        return action_probs, state_value
    
    def get_action_and_value(self, x):
        """Get action probabilities and state value"""
        action_probs, state_value = self.forward(x)
        return action_probs, state_value
    
    def get_action(self, x):
        """Sample action from policy"""
        action_probs, _ = self.forward(x)
        dist = torch.distributions.Categorical(action_probs)
        action = dist.sample()
        return action.item(), dist.log_prob(action), action_probs
    
    def evaluate_actions(self, x, actions):
        """Evaluate actions for PPO training"""
        action_probs, state_values = self.forward(x)
        dist = torch.distributions.Categorical(action_probs)
        
        action_log_probs = dist.log_prob(actions)
        entropy = dist.entropy()
        
        return action_log_probs, state_values.squeeze(), entropy
    
    def save(self, file_name='ppo_model.pth'):
        model_folder_path = './model'
        if not os.path.exists(model_folder_path):
            os.makedirs(model_folder_path)
        
        file_name = os.path.join(model_folder_path, file_name)
        torch.save(self.state_dict(), file_name)
    
    def load(self, file_name='ppo_model.pth'):
        model_folder_path = './model'
        file_name = os.path.join(model_folder_path, file_name)
        if os.path.exists(file_name):
            self.load_state_dict(torch.load(file_name))
