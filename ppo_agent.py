import torch
import numpy as np
from collections import deque
from game import SnakeGameAI, Direction, Point
from ppo_model import PPOActorCritic
from ppo_trainer import PPOTrainer
from helper import plot

# PPO Hyperparameters
LEARNING_RATE = 3e-4
GAMMA = 0.99
EPS_CLIP = 0.2
K_EPOCHS = 4
UPDATE_TIMESTEP = 512  # Update policy every n timesteps
MAX_EPISODES = 1000

class PPOAgent:
    def __init__(self):
        self.n_games = 0
        self.memory = []  # Store trajectory data
        self.timestep = 0
        
        # Initialize model and trainer
        self.model = PPOActorCritic(11, 256, 3)  # 11 state features, 3 actions
        self.trainer = PPOTrainer(
            self.model, 
            lr=LEARNING_RATE, 
            gamma=GAMMA, 
            eps_clip=EPS_CLIP, 
            k_epochs=K_EPOCHS
        )
        
        # For tracking performance
        self.episode_rewards = []
        self.episode_lengths = []
    
    def get_state(self, game):
        """Same state representation as DQN agent"""
        head = game.snake[0]
        point_l = Point(head.x - 20, head.y)
        point_r = Point(head.x + 20, head.y)
        point_u = Point(head.x, head.y - 20)
        point_d = Point(head.x, head.y + 20)
        
        dir_l = game.direction == Direction.LEFT
        dir_r = game.direction == Direction.RIGHT
        dir_u = game.direction == Direction.UP
        dir_d = game.direction == Direction.DOWN

        state = [
            # Danger straight
            (dir_r and game.is_collision(point_r)) or 
            (dir_l and game.is_collision(point_l)) or 
            (dir_u and game.is_collision(point_u)) or 
            (dir_d and game.is_collision(point_d)),

            # Danger right
            (dir_u and game.is_collision(point_r)) or 
            (dir_d and game.is_collision(point_l)) or 
            (dir_l and game.is_collision(point_u)) or 
            (dir_r and game.is_collision(point_d)),

            # Danger left
            (dir_d and game.is_collision(point_r)) or 
            (dir_u and game.is_collision(point_l)) or 
            (dir_r and game.is_collision(point_u)) or 
            (dir_l and game.is_collision(point_d)),
            
            # Move direction
            dir_l,
            dir_r,
            dir_u,
            dir_d,
            
            # Food location 
            game.food.x < game.head.x,  # food left
            game.food.x > game.head.x,  # food right
            game.food.y < game.head.y,  # food up
            game.food.y > game.head.y  # food down
        ]

        return np.array(state, dtype=int)
    
    def get_action(self, state):
        """Get action from PPO policy"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        with torch.no_grad():
            action, log_prob, action_probs = self.model.get_action(state_tensor)
            _, value = self.model.get_action_and_value(state_tensor)
        
        return action, log_prob.item(), value.item()
    
    def store_transition(self, state, action, log_prob, reward, done, value):
        """Store transition in memory"""
        self.memory.append({
            'state': state,
            'action': action,
            'log_prob': log_prob,
            'reward': reward,
            'done': done,
            'value': value
        })
    
    def update_policy(self):
        """Update policy using PPO"""
        if len(self.memory) == 0:
            return {}
        
        # Extract data from memory
        states = [transition['state'] for transition in self.memory]
        actions = [transition['action'] for transition in self.memory]
        log_probs = [transition['log_prob'] for transition in self.memory]
        rewards = [transition['reward'] for transition in self.memory]
        dones = [transition['done'] for transition in self.memory]
        values = [transition['value'] for transition in self.memory]
        
        # Train the model
        loss_info = self.trainer.train_step(states, actions, log_probs, 
                                          rewards, dones, values)
        
        # Clear memory
        self.memory = []
        
        return loss_info
    
    def action_to_move(self, action):
        """Convert action index to move format"""
        final_move = [0, 0, 0]
        final_move[action] = 1
        return final_move


def train_ppo():
    """Training function for PPO agent"""
    plot_scores = []
    plot_mean_scores = []
    total_score = 0
    record = 0
    agent = PPOAgent()
    game = SnakeGameAI()
    
    episode_reward = 0
    episode_length = 0
    
    print("Starting PPO Training...")
    
    while agent.n_games < MAX_EPISODES:
        # Get current state
        state = agent.get_state(game)
        
        # Get action from policy
        action, log_prob, value = agent.get_action(state)
        final_move = agent.action_to_move(action)
        
        # Perform action
        reward, done, score = game.play_step(final_move)
        episode_reward += reward
        episode_length += 1
        agent.timestep += 1
        
        # Store transition
        agent.store_transition(state, action, log_prob, reward, done, value)
        
        if done:
            # Episode finished
            agent.n_games += 1
            game.reset()
            
            # Track performance
            agent.episode_rewards.append(episode_reward)
            agent.episode_lengths.append(episode_length)
            
            if score > record:
                record = score
                agent.model.save()
            
            print(f'Game {agent.n_games}, Score: {score}, Record: {record}, '
                  f'Episode Reward: {episode_reward:.2f}, Length: {episode_length}')
            
            plot_scores.append(score)
            total_score += score
            mean_score = total_score / agent.n_games
            plot_mean_scores.append(mean_score)
            plot(plot_scores, plot_mean_scores)
            
            episode_reward = 0
            episode_length = 0
        
        # Update policy every UPDATE_TIMESTEP steps
        if agent.timestep % UPDATE_TIMESTEP == 0:
            loss_info = agent.update_policy()
            if loss_info:
                print(f"Policy updated at timestep {agent.timestep}")
                print(f"Losses - Actor: {loss_info['actor_loss']:.4f}, "
                      f"Critic: {loss_info['critic_loss']:.4f}, "
                      f"Entropy: {loss_info['entropy_loss']:.4f}")


if __name__ == '__main__':
    train_ppo()
